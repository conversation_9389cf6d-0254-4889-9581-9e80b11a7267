
const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');

async function quickTest() {
    try {
        await initializeFullIndex();
        const result = await executeFullIndexSearch({
            keywords: ["Android"],
            path: "终端安全"
        });
        
        // 分析结果
        const lines = result.data.split('\n');
        let detailedCount = 0;
        let simplifiedCount = 0;
        
        for (const line of lines) {
            if (line.includes('中发现相关内容，从第') && line.includes('行到第')) {
                simplifiedCount++;
            } else if (line.includes('文件:') && !line.includes('中发现相关内容')) {
                detailedCount++;
            }
        }
        
        console.log(`结果统计 (lineLimit=30):`);
        console.log(`- 详细显示的文件: ${detailedCount}`);
        console.log(`- 简化显示的文件: ${simplifiedCount}`);
        console.log(`- 总文件数: ${detailedCount + simplifiedCount}`);
        
    } catch (error) {
        console.error('测试出错:', error.message);
    }
}

quickTest();
        