const { readFile } = require('../chat-agent.js');
const fs = require('fs');
const path = require('path');

function testReadFileComprehensive() {
    console.log('=== read_file 工具综合测试 ===\n');
    
    // 创建测试文件
    const testFilePath = path.join(__dirname, 'comprehensive_test_file.txt');
    const testContent = `第1行：这是第一行内容
第2行：这是第二行内容
第3行：这是第三行内容
第4行：这是第四行内容
第5行：这是第五行内容`;
    
    fs.writeFileSync(testFilePath, testContent);
    console.log('创建测试文件，共5行内容\n');
    
    const testCases = [
        {
            name: '正常范围读取',
            lineRange: '2-4',
            expectedStatus: 200,
            description: '应该成功返回第2-4行内容'
        },
        {
            name: '起始行号超出文件大小',
            lineRange: '10-15',
            expectedStatus: 400,
            description: '应该返回错误：起始行号超出文件大小'
        },
        {
            name: '结束行号超出文件大小',
            lineRange: '3-10',
            expectedStatus: 200,
            description: '应该返回第3-5行内容，并说明结束行号超出'
        },
        {
            name: '只指定起始行号（正常）',
            lineRange: '4',
            expectedStatus: 200,
            description: '应该返回第4-5行内容'
        },
        {
            name: '只指定起始行号（超出）',
            lineRange: '10',
            expectedStatus: 400,
            description: '应该返回错误：起始行号超出文件大小'
        },
        {
            name: '无效行号格式',
            lineRange: 'abc-def',
            expectedStatus: 400,
            description: '应该返回错误：无效的行号格式'
        },
        {
            name: '起始行号为0',
            lineRange: '0-2',
            expectedStatus: 400,
            description: '应该返回错误：无效的行号格式'
        },
        {
            name: '负数行号',
            lineRange: '-1-2',
            expectedStatus: 400,
            description: '应该返回错误：无效的行号格式'
        },
        {
            name: '起始行号大于结束行号',
            lineRange: '5-2',
            expectedStatus: 400,
            description: '应该返回错误：无效的行号格式'
        }
    ];
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        console.log(`测试 ${index + 1}: ${testCase.name}`);
        console.log(`行号范围: ${testCase.lineRange}`);
        console.log(`期望: ${testCase.description}`);
        
        const result = readFile(testFilePath, testCase.lineRange);
        
        console.log(`实际状态码: ${result.status}`);
        console.log(`期望状态码: ${testCase.expectedStatus}`);
        
        if (result.status === testCase.expectedStatus) {
            console.log('✅ 测试通过');
            passedTests++;
        } else {
            console.log('❌ 测试失败');
        }
        
        console.log(`结果: ${result.data.substring(0, 100)}${result.data.length > 100 ? '...' : ''}`);
        console.log('---\n');
    });
    
    // 测试空文件
    console.log('测试 10: 空文件处理');
    const emptyFilePath = path.join(__dirname, 'empty_test_file.txt');
    fs.writeFileSync(emptyFilePath, '');
    
    const emptyFileResult = readFile(emptyFilePath, '1-1');
    console.log(`空文件测试 - 状态码: ${emptyFileResult.status}`);
    console.log(`空文件测试 - 结果: ${emptyFileResult.data}`);
    
    if (emptyFileResult.status === 400) {
        console.log('✅ 空文件测试通过');
        passedTests++;
        totalTests++;
    } else {
        console.log('❌ 空文件测试失败');
        totalTests++;
    }
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    fs.unlinkSync(emptyFilePath);
    console.log('\n测试文件已清理');
    
    console.log(`\n=== 测试结果总结 ===`);
    console.log(`通过: ${passedTests}/${totalTests}`);
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！read_file 工具的错误处理已完善');
    } else {
        console.log('⚠️  部分测试失败，需要进一步检查');
    }
}

testReadFileComprehensive();
