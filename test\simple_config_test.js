const fs = require('fs');
const path = require('path');

// 简单测试配置文件读取功能
function testConfigReading() {
    console.log('测试配置文件读取功能...\n');
    
    // 读取配置文件
    const configPath = path.join(__dirname, '../config.json');
    const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    console.log('配置文件内容:');
    console.log('- full_index_search.lineLimit:', config.tools.full_index_search.lineLimit);
    console.log('- 类型:', typeof config.tools.full_index_search.lineLimit);
    
    // 模拟代码中的逻辑
    function getLineLimitFromConfig(config, overrideValue = null) {
        if (overrideValue !== null) {
            return overrideValue;
        }
        
        return config.tools.full_index_search && config.tools.full_index_search.lineLimit 
            ? config.tools.full_index_search.lineLimit 
            : 50; // 默认值
    }
    
    console.log('\n测试不同情况:');
    console.log('1. 使用配置文件默认值:', getLineLimitFromConfig(config));
    console.log('2. 使用参数覆盖值 (10):', getLineLimitFromConfig(config, 10));
    console.log('3. 使用参数覆盖值 (100):', getLineLimitFromConfig(config, 100));
    
    // 测试配置文件缺失情况
    const emptyConfig = { tools: {} };
    console.log('4. 配置文件缺失时使用默认值:', getLineLimitFromConfig(emptyConfig));
    
    console.log('\n✅ 配置文件读取功能测试通过！');
}

testConfigReading();
