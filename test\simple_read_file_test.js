const fs = require('fs');
const path = require('path');

// 直接复制readFile函数进行测试
function readFile(location, lineRange) {
    // 支持绝对路径和相对路径
    let filePath = location;
    
    // 如果是相对路径，使用统一的路径处理逻辑
    if (!path.isAbsolute(location)) {
        // 由于工作目录已切换到documentPath，直接使用相对路径
        filePath = path.join(process.cwd(), location);
    }
    
    // 检查文件是否存在
    if (fs.existsSync(filePath)) {
        // 检查是否为目录
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
            // 如果是目录，列出目录内容
            try {
                const items = fs.readdirSync(filePath);
                if (items.length === 0) {
                    return { status: 200, data: `目录"${filePath}"为空` };
                } else {
                    return { status: 200, data: `目录"${filePath}"包含以下文件和目录:\n${items.map(item => `- ${item}`).join('\n')}` };
                }
            } catch (error) {
                return { status: 500, data: `无法读取目录"${filePath}": ${error.message}` };
            }
        }
        
        // 如果是文件，读取文件内容
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            
            // 如果指定了行号范围，只返回指定范围的内容
            if (lineRange) {
                const lines = content.split('\n');
                const [start, end] = lineRange.split('-').map(num => parseInt(num.trim(), 10));
                
                if (!isNaN(start) && !isNaN(end) && start > 0 && end > 0 && start <= end) {
                    const startIndex = start - 1; // 转换为0-based索引
                    const endIndex = Math.min(end, lines.length);
                    const selectedLines = lines.slice(startIndex, endIndex);
                    
                    // 为每行添加行号
                    const numberedLines = selectedLines.map((line, index) => `${startIndex + index + 1}: ${line}`);
                    content = numberedLines.join('\n');
                    return { status: 200, data: `文件"${filePath}"的第${start}-${end}行内容:\n${content}` };
                } else if (!isNaN(start) && start > 0) {
                    // 如果只指定了起始行
                    const startIndex = start - 1;
                    const selectedLines = lines.slice(startIndex);
                    
                    content = selectedLines.join('\n');
                    return { status: 200, data: `文件"${filePath}"的第${start}行及之后内容:\n${content}` };
                }
            }
            
            return { status: 200, data: `文件"${filePath}"的内容:\n${content}` };
        } catch (error) {
            return { status: 500, data: `读取文件"${filePath}"时出错: ${error.message}` };
        }
    } else {
        return { status: 404, data: `文件"${location}"不存在, 请更换正确的文件名称和路径` };
    }
}

function testReadFileLineOverflow() {
    console.log('测试 read_file 工具行号超出文件大小的情况...\n');
    
    // 创建一个测试文件
    const testFilePath = path.join(__dirname, 'temp_test_file.txt');
    const testContent = `第1行内容
第2行内容
第3行内容
第4行内容
第5行内容`;
    
    fs.writeFileSync(testFilePath, testContent);
    console.log('创建测试文件，共5行内容');
    
    // 测试用例1：正常范围
    console.log('\n测试用例1：正常范围 (1-3行)');
    const result1 = readFile(testFilePath, '1-3');
    console.log('结果1:', JSON.stringify(result1, null, 2));
    
    // 测试用例2：起始行号超出文件大小
    console.log('\n测试用例2：起始行号超出文件大小 (10-15行)');
    const result2 = readFile(testFilePath, '10-15');
    console.log('结果2:', JSON.stringify(result2, null, 2));
    
    // 测试用例3：起始行号正常，结束行号超出
    console.log('\n测试用例3：起始行号正常，结束行号超出 (3-10行)');
    const result3 = readFile(testFilePath, '3-10');
    console.log('结果3:', JSON.stringify(result3, null, 2));
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    console.log('\n测试文件已清理');
}

testReadFileLineOverflow();
