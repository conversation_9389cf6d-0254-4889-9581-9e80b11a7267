const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');
const path = require('path');

async function testLineLimitFeature() {
    console.log('开始测试 line_limit 功能...\n');

    // 初始化全文索引引擎
    try {
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
    } catch (error) {
        console.error('全文索引引擎初始化失败:', error);
        process.exit(1);
    }
    
    // 测试用例1：使用默认line_limit (50行)
    console.log('测试用例1：使用默认line_limit (50行)');
    const test1 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全"
    });
    console.log('测试结果1:', test1);
    console.log('----------------------------------------\n');

    // 测试用例2：设置较小的line_limit (10行)
    console.log('测试用例2：设置较小的line_limit (10行)');
    const test2 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全",
        line_limit: 10
    });
    console.log('测试结果2:', test2);
    console.log('----------------------------------------\n');

    // 测试用例3：设置较大的line_limit (100行)
    console.log('测试用例3：设置较大的line_limit (100行)');
    const test3 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全",
        line_limit: 100
    });
    console.log('测试结果3:', test3);
    console.log('----------------------------------------\n');

    // 测试用例4：设置极小的line_limit (1行)
    console.log('测试用例4：设置极小的line_limit (1行)');
    const test4 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全",
        line_limit: 1
    });
    console.log('测试结果4:', test4);
    console.log('----------------------------------------\n');

    // 测试用例5：测试多关键词搜索
    console.log('测试用例5：测试多关键词搜索，line_limit=20');
    const test5 = await executeFullIndexSearch({
        keywords: ["Android", "管控"],
        path: "终端安全",
        line_limit: 20
    });
    console.log('测试结果5:', test5);
    console.log('----------------------------------------\n');
}

// 运行测试
testLineLimitFeature().catch(console.error);
