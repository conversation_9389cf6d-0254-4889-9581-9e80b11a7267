// 测试分词的最大长度
const { CNFullText } = require('./indexer.js');

const engine = new CNFullText();

// 测试不同长度的词
const testTexts = [
  "短词：我爱你",                                    // 1-3字
  "中等词：北京大学计算机专业",                        // 4-6字  
  "长词：中华人民共和国国家发展改革委员会",              // 超过6字
  "超长词：中华人民共和国国家发展和改革委员会经济体制综合改革司办公室主任", // 很长的词
  "混合：OpenAI公司开发的GPT大语言模型技术"           // 中英文混合
];

console.log("=== 分词长度测试 ===");
testTexts.forEach((text, index) => {
  console.log(`\n文本 ${index + 1}: ${text}`);
  const tokens = engine._tokenize(text);
  
  // 统计不同长度的词
  const lengthStats = {};
  let maxLength = 0;
  
  tokens.forEach(token => {
    const len = token.length;
    lengthStats[len] = (lengthStats[len] || 0) + 1;
    maxLength = Math.max(maxLength, len);
  });
  
  console.log(`分词结果 (${tokens.length} 个): ${tokens.join(' | ')}`);
  console.log(`长度统计:`, lengthStats);
  console.log(`最长词长度: ${maxLength}字`);
  
  // 显示最长的几个词
  const longTokens = tokens.filter(t => t.length >= 4).sort((a, b) => b.length - a.length);
  if (longTokens.length > 0) {
    console.log(`长词示例 (≥4字): ${longTokens.slice(0, 5).join(' | ')}`);
  }
});

// 专门测试6字词的情况
console.log("\n=== 6字词测试 ===");
const sixCharTests = [
  "计算机科学技术",      // 正好6字
  "人工智能深度学习",    // 正好6字
  "北京大学清华大学",    // 正好6字
];

sixCharTests.forEach(text => {
  console.log(`\n测试文本: ${text} (${text.length}字)`);
  const tokens = engine._tokenize(text);
  const sixCharTokens = tokens.filter(t => t.length === 6);
  console.log(`6字词: ${sixCharTokens.join(' | ')}`);
  console.log(`所有分词: ${tokens.join(' | ')}`);
});
