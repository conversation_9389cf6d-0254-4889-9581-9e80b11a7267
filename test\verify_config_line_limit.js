const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

async function testDifferentConfigValues() {
    console.log('开始测试不同配置文件 lineLimit 值的效果...\n');

    const configPath = path.join(__dirname, '../config.json');
    const originalConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    
    // 测试不同的lineLimit值
    const testValues = [10, 30, 100];
    
    for (const lineLimit of testValues) {
        console.log(`\n=== 测试 lineLimit = ${lineLimit} ===`);
        
        // 修改配置文件
        const modifiedConfig = JSON.parse(JSON.stringify(originalConfig));
        modifiedConfig.tools.full_index_search.lineLimit = lineLimit;
        fs.writeFileSync(configPath, JSON.stringify(modifiedConfig, null, 2));
        console.log(`配置文件已修改，lineLimit 设置为 ${lineLimit}`);
        
        // 创建测试脚本
        const testScript = `
const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');

async function quickTest() {
    try {
        await initializeFullIndex();
        const result = await executeFullIndexSearch({
            keywords: ["Android"],
            path: "终端安全"
        });
        
        // 分析结果
        const lines = result.data.split('\\n');
        let detailedCount = 0;
        let simplifiedCount = 0;
        
        for (const line of lines) {
            if (line.includes('中发现相关内容，从第') && line.includes('行到第')) {
                simplifiedCount++;
            } else if (line.includes('文件:') && !line.includes('中发现相关内容')) {
                detailedCount++;
            }
        }
        
        console.log(\`结果统计 (lineLimit=${lineLimit}):\`);
        console.log(\`- 详细显示的文件: \${detailedCount}\`);
        console.log(\`- 简化显示的文件: \${simplifiedCount}\`);
        console.log(\`- 总文件数: \${detailedCount + simplifiedCount}\`);
        
    } catch (error) {
        console.error('测试出错:', error.message);
    }
}

quickTest();
        `;
        
        // 写入临时测试文件
        const tempTestPath = path.join(__dirname, 'temp_test.js');
        fs.writeFileSync(tempTestPath, testScript);
        
        // 运行测试
        await new Promise((resolve, reject) => {
            const child = spawn('node', [tempTestPath], {
                cwd: __dirname,
                stdio: 'inherit'
            });
            
            child.on('close', (code) => {
                if (code === 0) {
                    resolve();
                } else {
                    reject(new Error(`进程退出码: ${code}`));
                }
            });
            
            child.on('error', reject);
        });
        
        // 清理临时文件
        fs.unlinkSync(tempTestPath);
        
        // 等待一下再进行下一个测试
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 恢复原始配置
    console.log('\n=== 恢复原始配置 ===');
    fs.writeFileSync(configPath, JSON.stringify(originalConfig, null, 2));
    console.log('配置文件已恢复到原始状态');
    
    console.log('\n测试完成！');
    console.log('从测试结果可以看出：');
    console.log('- lineLimit 值越小，简化显示的文件越多');
    console.log('- lineLimit 值越大，详细显示的文件越多');
    console.log('- 配置文件的修改需要重新加载模块才能生效');
}

// 运行测试
testDifferentConfigValues().catch(console.error);
