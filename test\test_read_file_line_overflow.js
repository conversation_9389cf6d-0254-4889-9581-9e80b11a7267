const { readFile } = require('../chat-agent.js');
const fs = require('fs');
const path = require('path');

function testReadFileLineOverflow() {
    console.log('测试 read_file 工具行号超出文件大小的情况...\n');
    
    // 创建一个测试文件
    const testFilePath = path.join(__dirname, 'temp_test_file.txt');
    const testContent = `第1行内容
第2行内容
第3行内容
第4行内容
第5行内容`;
    
    fs.writeFileSync(testFilePath, testContent);
    console.log('创建测试文件，共5行内容');
    
    // 测试用例1：正常范围
    console.log('\n测试用例1：正常范围 (1-3行)');
    const result1 = readFile(testFilePath, '1-3');
    console.log('结果1:', result1);
    
    // 测试用例2：起始行号超出文件大小
    console.log('\n测试用例2：起始行号超出文件大小 (10-15行)');
    const result2 = readFile(testFilePath, '10-15');
    console.log('结果2:', result2);
    
    // 测试用例3：起始行号正常，结束行号超出
    console.log('\n测试用例3：起始行号正常，结束行号超出 (3-10行)');
    const result3 = readFile(testFilePath, '3-10');
    console.log('结果3:', result3);
    
    // 测试用例4：只指定起始行号，且超出文件大小
    console.log('\n测试用例4：只指定起始行号，且超出文件大小 (10行开始)');
    const result4 = readFile(testFilePath, '10');
    console.log('结果4:', result4);
    
    // 测试用例5：起始行号为0或负数
    console.log('\n测试用例5：起始行号为0 (0-2行)');
    const result5 = readFile(testFilePath, '0-2');
    console.log('结果5:', result5);
    
    // 测试用例6：起始行号为负数
    console.log('\n测试用例6：起始行号为负数 (-1-2行)');
    const result6 = readFile(testFilePath, '-1-2');
    console.log('结果6:', result6);
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    console.log('\n测试文件已清理');
    
    console.log('\n=== 测试总结 ===');
    console.log('当前发现的问题：');
    console.log('1. 当起始行号超出文件大小时，没有返回明确的错误信息');
    console.log('2. 当起始行号为0或负数时，没有适当的错误处理');
    console.log('3. 需要添加更好的边界检查和错误提示');
}

testReadFileLineOverflow();
