const { readFile } = require('../chat-agent.js');
const fs = require('fs');
const path = require('path');

function testReadFileEdgeCases() {
    console.log('测试 read_file 工具的边界情况...\n');
    
    // 创建一个测试文件
    const testFilePath = path.join(__dirname, 'temp_test_file.txt');
    const testContent = `第1行内容
第2行内容
第3行内容`;
    
    fs.writeFileSync(testFilePath, testContent);
    console.log('创建测试文件，共3行内容\n');
    
    // 测试用例1：请求最后一行
    console.log('测试用例1：请求最后一行 (3-3行)');
    const result1 = readFile(testFilePath, '3-3');
    console.log('状态码:', result1.status);
    console.log('结果:', result1.data + '\n');
    
    // 测试用例2：请求超出一行
    console.log('测试用例2：请求超出一行 (4-4行)');
    const result2 = readFile(testFilePath, '4-4');
    console.log('状态码:', result2.status);
    console.log('结果:', result2.data + '\n');
    
    // 测试用例3：请求从最后一行开始
    console.log('测试用例3：请求从最后一行开始 (3行开始)');
    const result3 = readFile(testFilePath, '3');
    console.log('状态码:', result3.status);
    console.log('结果:', result3.data + '\n');
    
    // 测试用例4：请求从超出的行开始
    console.log('测试用例4：请求从超出的行开始 (4行开始)');
    const result4 = readFile(testFilePath, '4');
    console.log('状态码:', result4.status);
    console.log('结果:', result4.data + '\n');
    
    // 测试用例5：负数行号
    console.log('测试用例5：负数行号 (-1-2行)');
    const result5 = readFile(testFilePath, '-1-2');
    console.log('状态码:', result5.status);
    console.log('结果:', result5.data + '\n');
    
    // 测试用例6：空文件
    const emptyFilePath = path.join(__dirname, 'empty_test_file.txt');
    fs.writeFileSync(emptyFilePath, '');
    console.log('测试用例6：空文件 (1-1行)');
    const result6 = readFile(emptyFilePath, '1-1');
    console.log('状态码:', result6.status);
    console.log('结果:', result6.data + '\n');
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    fs.unlinkSync(emptyFilePath);
    console.log('测试文件已清理');
    
    console.log('\n=== 边界情况测试完成 ===');
}

testReadFileEdgeCases();
