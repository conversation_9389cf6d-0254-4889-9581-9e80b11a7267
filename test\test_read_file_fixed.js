const { readFile } = require('../chat-agent.js');
const fs = require('fs');
const path = require('path');

function testReadFileFixed() {
    console.log('测试修复后的 read_file 工具...\n');
    
    // 创建一个测试文件
    const testFilePath = path.join(__dirname, 'temp_test_file.txt');
    const testContent = `第1行内容
第2行内容
第3行内容
第4行内容
第5行内容`;
    
    fs.writeFileSync(testFilePath, testContent);
    console.log('创建测试文件，共5行内容\n');
    
    // 测试用例1：正常范围
    console.log('测试用例1：正常范围 (1-3行)');
    const result1 = readFile(testFilePath, '1-3');
    console.log('状态码:', result1.status);
    console.log('结果:', result1.data.substring(0, 100) + '...\n');
    
    // 测试用例2：起始行号超出文件大小
    console.log('测试用例2：起始行号超出文件大小 (10-15行)');
    const result2 = readFile(testFilePath, '10-15');
    console.log('状态码:', result2.status);
    console.log('结果:', result2.data + '\n');
    
    // 测试用例3：起始行号正常，结束行号超出
    console.log('测试用例3：起始行号正常，结束行号超出 (3-10行)');
    const result3 = readFile(testFilePath, '3-10');
    console.log('状态码:', result3.status);
    console.log('结果:', result3.data.substring(0, 150) + '...\n');
    
    // 测试用例4：只指定起始行号，且超出文件大小
    console.log('测试用例4：只指定起始行号，且超出文件大小 (10行开始)');
    const result4 = readFile(testFilePath, '10');
    console.log('状态码:', result4.status);
    console.log('结果:', result4.data + '\n');
    
    // 测试用例5：起始行号为0
    console.log('测试用例5：起始行号为0 (0-2行)');
    const result5 = readFile(testFilePath, '0-2');
    console.log('状态码:', result5.status);
    console.log('结果:', result5.data + '\n');
    
    // 测试用例6：无效的行号格式
    console.log('测试用例6：无效的行号格式 (abc-def)');
    const result6 = readFile(testFilePath, 'abc-def');
    console.log('状态码:', result6.status);
    console.log('结果:', result6.data + '\n');
    
    // 测试用例7：起始行号大于结束行号
    console.log('测试用例7：起始行号大于结束行号 (5-2行)');
    const result7 = readFile(testFilePath, '5-2');
    console.log('状态码:', result7.status);
    console.log('结果:', result7.data + '\n');
    
    // 清理测试文件
    fs.unlinkSync(testFilePath);
    console.log('测试文件已清理');
    
    console.log('\n=== 测试总结 ===');
    console.log('✅ 修复完成：');
    console.log('1. 当起始行号超出文件大小时，返回状态码400和明确错误信息');
    console.log('2. 当结束行号超出文件大小时，返回实际内容并说明超出情况');
    console.log('3. 添加了行号格式验证');
    console.log('4. 改进了错误提示信息的可读性');
}

testReadFileFixed();
