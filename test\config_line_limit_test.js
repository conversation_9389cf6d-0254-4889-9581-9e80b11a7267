const { executeFullIndexSearch, initializeFullIndex } = require('../chat-agent.js');
const fs = require('fs');
const path = require('path');

async function testConfigLineLimit() {
    console.log('开始测试从配置文件读取 line_limit 功能...\n');

    // 读取当前配置文件
    const configPath = path.join(__dirname, '../config.json');
    const originalConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
    console.log('当前配置文件中的 lineLimit:', originalConfig.tools.full_index_search.lineLimit);

    // 初始化全文索引引擎
    try {
        await initializeFullIndex();
        console.log('全文索引引擎初始化完成\n');
    } catch (error) {
        console.error('全文索引引擎初始化失败:', error);
        process.exit(1);
    }
    
    // 测试用例1：使用配置文件中的默认lineLimit
    console.log('测试用例1：使用配置文件中的默认lineLimit (应该是50)');
    const test1 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全"
    });
    console.log('测试结果1 (部分):', test1.data.substring(0, 500) + '...');
    console.log('----------------------------------------\n');

    // 测试用例2：修改配置文件中的lineLimit为10，然后重新加载
    console.log('测试用例2：临时修改配置文件中的lineLimit为10');
    const modifiedConfig = JSON.parse(JSON.stringify(originalConfig));
    modifiedConfig.tools.full_index_search.lineLimit = 10;
    fs.writeFileSync(configPath, JSON.stringify(modifiedConfig, null, 2));
    
    // 重新加载模块（注意：这在实际应用中可能需要重启进程）
    console.log('注意：配置文件已修改，但需要重启进程才能生效');
    console.log('当前测试仍使用原来加载的配置值');
    
    const test2 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全"
    });
    console.log('测试结果2 (部分):', test2.data.substring(0, 500) + '...');
    console.log('----------------------------------------\n');

    // 测试用例3：通过参数覆盖配置文件中的值
    console.log('测试用例3：通过参数覆盖配置文件中的值 (line_limit=1)');
    const test3 = await executeFullIndexSearch({
        keywords: ["Android"],
        path: "终端安全",
        line_limit: 1
    });
    console.log('测试结果3 (部分):', test3.data.substring(0, 500) + '...');
    console.log('----------------------------------------\n');

    // 恢复原始配置文件
    console.log('恢复原始配置文件...');
    fs.writeFileSync(configPath, JSON.stringify(originalConfig, null, 2));
    console.log('配置文件已恢复');
    
    console.log('\n测试完成！');
    console.log('总结：');
    console.log('1. 配置文件中的 lineLimit 值已成功添加');
    console.log('2. 代码已修改为从配置文件读取默认值');
    console.log('3. 参数传递的 line_limit 可以覆盖配置文件中的值');
    console.log('4. 如需修改配置文件中的值生效，需要重启应用程序');
}

// 运行测试
testConfigLineLimit().catch(console.error);
